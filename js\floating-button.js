// Floating Action Button Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const floatingActionBtn = document.getElementById('floatingActionBtn');
    const floatingMenu = document.getElementById('floatingMenu');

    // All media buttons
    const selectFilesBtn = document.getElementById('selectFilesBtn');
    const selectFolderFloatingBtn = document.getElementById('selectFolderFloatingBtn');

    // Images only buttons
    const selectImagesOnlyBtn = document.getElementById('selectImagesOnlyBtn');
    const selectFolderImagesBtn = document.getElementById('selectFolderImagesBtn');

    // Videos only buttons
    const selectVideosOnlyBtn = document.getElementById('selectVideosOnlyBtn');
    const selectFolderVideosBtn = document.getElementById('selectFolderVideosBtn');

    // Input elements
    const folderInput = document.getElementById('folderInput');
    const folderDirectoryInput = document.getElementById('folderDirectoryInput');
    const imagesOnlyInput = document.getElementById('imagesOnlyInput');
    const videosOnlyInput = document.getElementById('videosOnlyInput');
    const folderImagesOnlyInput = document.getElementById('folderImagesOnlyInput');
    const folderVideosOnlyInput = document.getElementById('folderVideosOnlyInput');

    // Make sure essential elements exist before adding event listeners
    if (!floatingActionBtn || !floatingMenu) {
        console.error('Essential floating button elements not found');
        return;
    }

    // Toggle floating menu
    floatingActionBtn.addEventListener('click', function(e) {
        // Check if we're dragging - if so, don't toggle the menu
        if (floatingActionBtn.classList.contains('dragging')) {
            return;
        }

        e.preventDefault();
        e.stopPropagation();
        floatingActionBtn.classList.toggle('active');
        floatingMenu.classList.toggle('active');

        // Force display of menu when button is active
        if (floatingActionBtn.classList.contains('active')) {
            floatingMenu.style.display = 'flex';

            // Update the position of the floating menu before showing it
            if (typeof window.updateFloatingMenuPosition === 'function') {
                window.updateFloatingMenuPosition();
            }
        } else {
            setTimeout(() => {
                if (!floatingActionBtn.classList.contains('active')) {
                    floatingMenu.style.display = 'none';
                }
            }, 300);
        }
    });

    // Helper function to handle button clicks
    function handleButtonClick(e, inputElement) {
        e.preventDefault();
        e.stopPropagation();
        if (inputElement) {
            inputElement.click();
        }
        floatingActionBtn.classList.remove('active');
        floatingMenu.classList.remove('active');
        setTimeout(() => {
            floatingMenu.style.display = 'none';
        }, 300);
    }

    // Handle select files button click (all media)
    if (selectFilesBtn) {
        selectFilesBtn.addEventListener('click', function(e) {
            handleButtonClick(e, folderInput);
        });
    }

    // Handle select folder button click (all media)
    if (selectFolderFloatingBtn) {
        selectFolderFloatingBtn.addEventListener('click', function(e) {
            handleButtonClick(e, folderDirectoryInput);
        });
    }

    // Handle select images only button click
    if (selectImagesOnlyBtn) {
        selectImagesOnlyBtn.addEventListener('click', function(e) {
            handleButtonClick(e, imagesOnlyInput);
        });
    }

    // Handle select videos only button click
    if (selectVideosOnlyBtn) {
        selectVideosOnlyBtn.addEventListener('click', function(e) {
            handleButtonClick(e, videosOnlyInput);
        });
    }

    // Handle select folder images only button click
    if (selectFolderImagesBtn) {
        selectFolderImagesBtn.addEventListener('click', function(e) {
            handleButtonClick(e, folderImagesOnlyInput);
        });
    }

    // Handle select folder videos only button click
    if (selectFolderVideosBtn) {
        selectFolderVideosBtn.addEventListener('click', function(e) {
            handleButtonClick(e, folderVideosOnlyInput);
        });
    }

    // Handle tab switching
    const contentTab = document.getElementById('content-tab');
    const tableTab = document.getElementById('table-tab');
    const settingsTab = document.getElementById('settings-tab');

    // Show floating button when switching to content tab
    contentTab.addEventListener('shown.bs.tab', function() {
        // Check if we're in metadata subtab
        if (metadataContent && metadataContent.classList.contains('active')) {
            setTimeout(() => {
                floatingActionBtn.style.display = 'flex';
                // Update floating menu position
                if (typeof window.updateFloatingMenuPosition === 'function') {
                    window.updateFloatingMenuPosition();
                }
            }, 300);
        }
    });

    // Hide floating button when switching to other tabs
    tableTab.addEventListener('shown.bs.tab', function() {
        floatingActionBtn.style.display = 'none';
        floatingMenu.style.display = 'none';
        floatingMenu.classList.remove('active');
        floatingActionBtn.classList.remove('active');
    });

    settingsTab.addEventListener('shown.bs.tab', function() {
        floatingActionBtn.style.display = 'none';
        floatingMenu.style.display = 'none';
        floatingMenu.classList.remove('active');
        floatingActionBtn.classList.remove('active');
    });

    // Handle content sub-tabs
    const metadataSubtab = document.getElementById('metadata-subtab');
    const imagenSubtab = document.getElementById('imagen-subtab');
    const prompterSubtab = document.getElementById('prompter-subtab');
    const metadataContent = document.getElementById('metadata-content');
    const imagenContent = document.getElementById('imagen-content');
    const prompterContent = document.getElementById('prompter-content');

    // Use data-bs-toggle="pill" for tab switching instead of manual class toggling
    // Just add event listeners for showing/hiding the floating button

    metadataSubtab.addEventListener('shown.bs.tab', function() {
        // Show floating button in metadata tab
        setTimeout(() => {
            floatingActionBtn.style.display = 'flex';
            // Update floating menu position
            if (typeof window.updateFloatingMenuPosition === 'function') {
                window.updateFloatingMenuPosition();
            }
            // Restore position if needed
            if (typeof window.restoreElementPosition === 'function') {
                window.restoreElementPosition('floatingActionBtn', 'floatingBtnPosition');
            }
        }, 300);
    });

    imagenSubtab.addEventListener('shown.bs.tab', function() {
        // Hide floating button in imagen tab
        floatingActionBtn.style.display = 'none';
        floatingMenu.style.display = 'none';
        floatingMenu.classList.remove('active');
        floatingActionBtn.classList.remove('active');
    });

    prompterSubtab.addEventListener('shown.bs.tab', function() {
        // Hide floating button in prompter tab
        floatingActionBtn.style.display = 'none';
        floatingMenu.style.display = 'none';
        floatingMenu.classList.remove('active');
        floatingActionBtn.classList.remove('active');
    });

    // Close floating menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!floatingActionBtn.contains(event.target) &&
            !floatingMenu.contains(event.target) &&
            floatingMenu.classList.contains('active')) {
            floatingActionBtn.classList.remove('active');
            floatingMenu.classList.remove('active');
            setTimeout(() => {
                if (!floatingActionBtn.classList.contains('active')) {
                    floatingMenu.style.display = 'none';
                }
            }, 300);
        }
    });

    // Initialize floating menu display property
    floatingMenu.style.display = 'none';

    // Initialize button visibility and position based on active tab
    setTimeout(() => {
        // Check if we're in content tab
        const contentTabActive = document.getElementById('content').classList.contains('active') ||
                                document.getElementById('content').classList.contains('show');

        // Check which subtab is active
        const metadataActive = metadataContent && metadataContent.classList.contains('active');
        const imagenActive = imagenContent && imagenContent.classList.contains('active');
        const prompterActive = prompterContent && prompterContent.classList.contains('active');

        if (contentTabActive && metadataActive) {
            // Show floating button if we're in metadata tab
            floatingActionBtn.style.display = 'flex';

            // Restore position from localStorage
            if (typeof window.restoreElementPosition === 'function') {
                window.restoreElementPosition('floatingActionBtn', 'floatingBtnPosition');
            }

            // Initialize floating menu position
            if (typeof window.updateFloatingMenuPosition === 'function') {
                window.updateFloatingMenuPosition();
            }
        } else {
            // Hide floating button if we're in other tabs
            floatingActionBtn.style.display = 'none';
            floatingMenu.style.display = 'none';
        }
    }, 500);
});
